#include <scheduler.h>
#include "stm32f10x.h"  
#include "stm32f10x_tim.h"
#include "stm32f10x_rcc.h"
#include <misc.h>
#include <led.h>
#include <systick.h>
#include <key.h>
#include <OLED.h>
#include <oled_app.h>
#include <stdio.h>   // ������������� vsnprintf
#include <stdarg.h>  // ������������� va_list, va_start, va_end
#include <stdint.h>  // �Ƽ����������� uint8_t ����
#include <sensorcounter.h>
#include <Timer.h>
#include <pwm.h>


extern uint8_t led2_lightflag;
extern uint8_t ucled[3];
extern uint8_t beep_enable;
extern uint8_t time_200ms;
extern uint8_t led2flag;
extern uint16_t num;
extern uint8_t angel;









