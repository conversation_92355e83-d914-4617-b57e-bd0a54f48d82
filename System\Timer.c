#include <Timer.h>
#include <misc.h>

                                      //��ʱ������//
/*rcc����ʱ��--ѡ��ʱ����Ԫ��ʱ��Դ���ڶ�ʱ�ж�ѡ���ڲ�ʱ��Դ--����ʱ����Ԫ��pscԤ��Ƶ��arr�Զ���װ��cnt��������
--��������жϿ������������ж������NVIC--����NVIC--���п���--ʹ�ܼ�����*/



void Timer_init(void)
{
RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2,ENABLE);
	
TIM_InternalClockConfig(TIM2);

TIM_TimeBaseInitTypeDef TIM_TimeBaseInitstructure;
TIM_TimeBaseInitstructure.TIM_ClockDivision=TIM_CKD_DIV1;
TIM_TimeBaseInitstructure.TIM_CounterMode=TIM_CounterMode_Up;   //����ģʽ
TIM_TimeBaseInitstructure.TIM_Period=20000-1;            //ARR  
TIM_TimeBaseInitstructure.TIM_Prescaler=72-1;          //PSC
TIM_TimeBaseInitstructure.TIM_RepetitionCounter=0;  //�߼���ʱ������//
TIM_TimeBaseInit(TIM2,&TIM_TimeBaseInitstructure);
	
	TIM_ClearFlag(TIM2,TIM_FLAG_Update);//

TIM_ITConfig(TIM2,TIM_IT_Update,ENABLE);
	
NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
NVIC_InitTypeDef NVIC_InitStructure;
NVIC_InitStructure.NVIC_IRQChannel=TIM2_IRQn;                   
NVIC_InitStructure.NVIC_IRQChannelCmd=ENABLE;
NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority=2;
NVIC_InitStructure.NVIC_IRQChannelSubPriority=1;
NVIC_Init(&NVIC_InitStructure);


TIM_Cmd(TIM2,ENABLE);	
}


// 现在是 语法 问题 只是括号







