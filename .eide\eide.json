{"name": "demo_01", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "Start", "files": [{"path": "Start/startup_stm32f10x_md.s"}, {"path": "Start/core_cm3.c"}, {"path": "Start/core_cm3.h"}, {"path": "Start/stm32f10x.h"}, {"path": "Start/system_stm32f10x.c"}, {"path": "Start/system_stm32f10x.h"}], "folders": []}, {"name": "Library", "files": [{"path": "Library/misc.c"}, {"path": "Library/misc.h"}, {"path": "Library/stm32f10x_adc.c"}, {"path": "Library/stm32f10x_adc.h"}, {"path": "Library/stm32f10x_bkp.c"}, {"path": "Library/stm32f10x_bkp.h"}, {"path": "Library/stm32f10x_can.c"}, {"path": "Library/stm32f10x_can.h"}, {"path": "Library/stm32f10x_cec.c"}, {"path": "Library/stm32f10x_cec.h"}, {"path": "Library/stm32f10x_crc.c"}, {"path": "Library/stm32f10x_crc.h"}, {"path": "Library/stm32f10x_dac.c"}, {"path": "Library/stm32f10x_dac.h"}, {"path": "Library/stm32f10x_dbgmcu.c"}, {"path": "Library/stm32f10x_dbgmcu.h"}, {"path": "Library/stm32f10x_dma.c"}, {"path": "Library/stm32f10x_dma.h"}, {"path": "Library/stm32f10x_exti.c"}, {"path": "Library/stm32f10x_exti.h"}, {"path": "Library/stm32f10x_flash.c"}, {"path": "Library/stm32f10x_flash.h"}, {"path": "Library/stm32f10x_fsmc.c"}, {"path": "Library/stm32f10x_fsmc.h"}, {"path": "Library/stm32f10x_gpio.c"}, {"path": "Library/stm32f10x_gpio.h"}, {"path": "Library/stm32f10x_i2c.c"}, {"path": "Library/stm32f10x_i2c.h"}, {"path": "Library/stm32f10x_iwdg.c"}, {"path": "Library/stm32f10x_iwdg.h"}, {"path": "Library/stm32f10x_pwr.c"}, {"path": "Library/stm32f10x_pwr.h"}, {"path": "Library/stm32f10x_rcc.c"}, {"path": "Library/stm32f10x_rcc.h"}, {"path": "Library/stm32f10x_rtc.c"}, {"path": "Library/stm32f10x_rtc.h"}, {"path": "Library/stm32f10x_sdio.c"}, {"path": "Library/stm32f10x_sdio.h"}, {"path": "Library/stm32f10x_spi.c"}, {"path": "Library/stm32f10x_spi.h"}, {"path": "Library/stm32f10x_tim.c"}, {"path": "Library/stm32f10x_tim.h"}, {"path": "Library/stm32f10x_usart.c"}, {"path": "Library/stm32f10x_usart.h"}, {"path": "Library/stm32f10x_wwdg.c"}, {"path": "Library/stm32f10x_wwdg.h"}], "folders": []}, {"name": "System", "files": [{"path": "System/Timer.c"}], "folders": []}, {"name": "component", "files": [{"path": "component/OLED.c"}, {"path": "component/OLED.h"}, {"path": "component/OLED_Font.h"}], "folders": []}, {"name": "User", "files": [{"path": "main.c"}, {"path": "User/stm32f10x_conf.h"}, {"path": "User/stm32f10x_it.c"}, {"path": "User/stm32f10x_it.h"}], "folders": []}, {"name": "Driver", "files": [{"path": "Driver/scheduler.c"}, {"path": "Driver/mydefine.h"}, {"path": "Driver/led.c"}, {"path": "Driver/systick.c"}, {"path": "Driver/key.c"}, {"path": "Driver/oled_app.c"}, {"path": "Driver/sensorcounter.c"}, {"path": "Driver/pwm.c"}], "folders": []}]}, "outDir": "build", "deviceName": "STM32F103C8", "packDir": ".pack/Keil/STM32F1xx_DFP.2.3.0", "miscInfo": {"uid": "beb2072bbe88c747a49bded39149221a"}, "targets": {"demo_01": {"excludeList": [], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M3", "archExtensions": "", "floatingPointHardware": "none", "useCustomScatterFile": false, "scatterFilePath": "<YOUR_SCATTER_FILE>.sct", "storageLayout": {"RAM": [{"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x5000"}, "isChecked": true, "noInit": false}], "ROM": [{"tag": "IROM", "id": 1, "mem": {"startAddr": "0x08000000", "size": "0x10000"}, "isChecked": true, "isStartup": true}]}, "options": "null"}, "uploader": "STLink", "uploadConfig": {"bin": "", "proType": "SWD", "resetMode": "default", "runAfterProgram": true, "speed": 4000, "address": "0x08000000", "elFile": "None", "optionBytes": ".eide/demo_01.st.option.bytes.ini", "otherCmds": ""}, "uploadConfigMap": {"JLink": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}}, "custom_dep": {"name": "default", "incList": [".", "Start", "User", "Library", "Driver", "component", "System", ".cmsis/include", "RTE/_demo_01"], "libList": [], "defineList": ["USE_STDPERIPH_DRIVER", "STM32F10X_MD"]}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-0", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings", "gnu-extensions": true}, "asm-compiler": {}, "linker": {"output-format": "elf", "ro-base": "0x08000000", "rw-base": "0x20000000"}}, "AC6": {"version": 3, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"use-microLIB": false, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-0", "language-c": "c99", "language-cpp": "c++11", "link-time-optimization": false}, "asm-compiler": {"$use": "asm-auto"}, "linker": {"output-format": "elf", "misc-controls": "--diag_suppress=L6329"}}}}}, "version": "3.6"}